import React from 'react';
import Image from 'next/image';

interface MusicPlatformLinksProps {
  links?: string | null;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const MusicPlatformLinks: React.FC<MusicPlatformLinksProps> = ({ 
  links, 
  className = '',
  size = 'md'
}) => {
  if (!links) return null;

  let parsedLinks: Record<string, string[]>;
  try {
    parsedLinks = JSON.parse(links);
  } catch (error) {
    console.error('Failed to parse links:', error);
    return null;
  }

  const spotifyLinks = parsedLinks.spotify || [];
  const appleMusicLinks = parsedLinks.applemusic || [];

  if (spotifyLinks.length === 0 && appleMusicLinks.length === 0) {
    return null;
  }

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-10 h-10'
  };

  const iconSize = sizeClasses[size];
  const pixelSize = size === 'sm' ? 20 : size === 'md' ? 32 : 40;

  const handleLinkClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {spotifyLinks.length > 0 && (
        <button
          onClick={() => handleLinkClick(spotifyLinks[0])}
          className="transition-transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 rounded"
          title="Listen on Spotify"
        >
          <Image
            src="/spotify.svg"
            alt="Spotify"
            width={pixelSize}
            height={pixelSize}
            className={`${iconSize} object-contain filter dark:invert`}
          />
        </button>
      )}
      {appleMusicLinks.length > 0 && (
        <button
          onClick={() => handleLinkClick(appleMusicLinks[0])}
          className="transition-transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 rounded"
          title="Listen on Apple Music"
        >
          <Image
            src="/apple-music.svg"
            alt="Apple Music"
            width={pixelSize}
            height={pixelSize}
            className={`${iconSize} object-contain filter dark:invert`}
          />
        </button>
      )}
    </div>
  );
};
